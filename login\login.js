// Login Page Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const loginForm = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const togglePassword = document.getElementById('togglePassword');
    const rememberCheckbox = document.getElementById('remember');
    const loginBtn = loginForm.querySelector('.login-btn');
    const googleLoginBtn = document.getElementById('googleLogin');
    const facebookLoginBtn = document.getElementById('facebookLogin');

    // Initialize form
    initializeForm();

    // Form submission handler
    loginForm.addEventListener('submit', handleFormSubmission);

    // Toggle password visibility
    togglePassword.addEventListener('click', togglePasswordVisibility);

    // Social login handlers
    googleLoginBtn.addEventListener('click', handleGoogleLogin);
    facebookLoginBtn.addEventListener('click', handleFacebookLogin);

    // Input validation and animations
    setupInputValidation();

    // Remember me functionality
    loadRememberedCredentials();

    function initializeForm() {
        // Add input animations
        const inputs = document.querySelectorAll('.input-wrapper input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });

        // Add floating label effect
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                if (this.value) {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        });
    }

    function handleFormSubmission(e) {
        e.preventDefault();

        // Get form data
        const formData = {
            email: emailInput.value.trim(),
            password: passwordInput.value,
            remember: rememberCheckbox.checked
        };

        // Validate form
        if (!validateForm(formData)) {
            return;
        }

        // Show loading state
        setLoadingState(true);

        // Simulate login API call
        simulateLogin(formData)
            .then(response => {
                handleLoginSuccess(response, formData);
            })
            .catch(error => {
                handleLoginError(error);
            })
            .finally(() => {
                setLoadingState(false);
            });
    }

    function validateForm(data) {
        let isValid = true;

        // Email validation
        if (!data.email) {
            showFieldError(emailInput, 'يرجى إدخال البريد الإلكتروني');
            isValid = false;
        } else if (!isValidEmail(data.email)) {
            showFieldError(emailInput, 'يرجى إدخال بريد إلكتروني صحيح');
            isValid = false;
        } else {
            clearFieldError(emailInput);
        }

        // Password validation
        if (!data.password) {
            showFieldError(passwordInput, 'يرجى إدخال كلمة المرور');
            isValid = false;
        } else if (data.password.length < 6) {
            showFieldError(passwordInput, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            isValid = false;
        } else {
            clearFieldError(passwordInput);
        }

        return isValid;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showFieldError(input, message) {
        // Remove existing error
        clearFieldError(input);

        // Add error styling
        input.style.borderColor = '#f44336';
        input.parentElement.style.borderColor = '#f44336';

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            color: #f44336;
            font-size: 0.85rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        `;

        // Add error icon
        const errorIcon = document.createElement('i');
        errorIcon.className = 'fas fa-exclamation-circle';
        errorDiv.insertBefore(errorIcon, errorDiv.firstChild);

        // Insert error message
        input.parentElement.parentElement.appendChild(errorDiv);

        // Shake animation
        input.parentElement.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            input.parentElement.style.animation = '';
        }, 500);
    }

    function clearFieldError(input) {
        // Reset styling
        input.style.borderColor = '';
        input.parentElement.style.borderColor = '';

        // Remove error message
        const errorDiv = input.parentElement.parentElement.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    function setLoadingState(isLoading) {
        if (isLoading) {
            loginBtn.disabled = true;
            loginBtn.innerHTML = `
                <div class="loading-spinner"></div>
                <span>جاري تسجيل الدخول...</span>
            `;
        } else {
            loginBtn.disabled = false;
            loginBtn.innerHTML = `
                <span>تسجيل الدخول</span>
                <i class="fas fa-arrow-left"></i>
            `;
        }
    }

    function simulateLogin(data) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Simulate different scenarios
                if (data.email === '<EMAIL>' && data.password === 'admin123') {
                    resolve({
                        success: true,
                        user: {
                            name: 'مدير النظام',
                            email: data.email,
                            role: 'admin'
                        },
                        token: 'fake-jwt-token'
                    });
                } else if (data.email === '<EMAIL>' && data.password === 'patient123') {
                    resolve({
                        success: true,
                        user: {
                            name: 'أحمد محمد',
                            email: data.email,
                            role: 'patient'
                        },
                        token: 'fake-jwt-token'
                    });
                } else if (data.email === '<EMAIL>') {
                    reject({
                        error: 'البريد الإلكتروني غير مسجل في النظام'
                    });
                } else {
                    reject({
                        error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
                    });
                }
            }, 2000);
        });
    }

    function handleLoginSuccess(response, formData) {
        // Save credentials if remember me is checked
        if (formData.remember) {
            saveCredentials(formData.email);
        } else {
            clearSavedCredentials();
        }

        // Save user data
        localStorage.setItem('user', JSON.stringify(response.user));
        localStorage.setItem('token', response.token);

        // Show success message
        showNotification('تم تسجيل الدخول بنجاح!', 'success');

        // Redirect after delay
        setTimeout(() => {
            window.location.href = '../index.html';
        }, 1500);
    }

    function handleLoginError(error) {
        showNotification(error.error || 'حدث خطأ أثناء تسجيل الدخول', 'error');
    }

    function togglePasswordVisibility() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        // Toggle eye icon
        const icon = togglePassword.querySelector('i');
        if (type === 'password') {
            icon.className = 'fas fa-eye';
        } else {
            icon.className = 'fas fa-eye-slash';
        }
    }

    function handleGoogleLogin() {
        showNotification('سيتم توجيهك إلى Google للمصادقة...', 'info');
        // Implement Google OAuth here
        setTimeout(() => {
            showNotification('تم تسجيل الدخول بنجاح عبر Google!', 'success');
        }, 2000);
    }

    function handleFacebookLogin() {
        showNotification('سيتم توجيهك إلى Facebook للمصادقة...', 'info');
        // Implement Facebook OAuth here
        setTimeout(() => {
            showNotification('تم تسجيل الدخول بنجاح عبر Facebook!', 'success');
        }, 2000);
    }

    function setupInputValidation() {
        // Real-time email validation
        emailInput.addEventListener('input', function() {
            if (this.value && !isValidEmail(this.value)) {
                this.style.borderColor = '#ff9800';
            } else {
                this.style.borderColor = '';
            }
        });

        // Password strength indicator
        passwordInput.addEventListener('input', function() {
            const strength = getPasswordStrength(this.value);
            updatePasswordStrengthIndicator(strength);
        });
    }

    function getPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 6) strength++;
        if (password.length >= 8) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    }

    function updatePasswordStrengthIndicator(strength) {
        // Remove existing indicator
        const existingIndicator = document.querySelector('.password-strength');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        if (passwordInput.value.length === 0) return;

        // Create strength indicator
        const indicator = document.createElement('div');
        indicator.className = 'password-strength';
        indicator.style.cssText = `
            margin-top: 0.5rem;
            display: flex;
            gap: 0.25rem;
            align-items: center;
        `;

        const strengthLevels = ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'];
        const strengthColors = ['#f44336', '#ff9800', '#ffc107', '#4caf50', '#2e7d32'];

        // Create strength bars
        for (let i = 0; i < 5; i++) {
            const bar = document.createElement('div');
            bar.style.cssText = `
                width: 20px;
                height: 4px;
                border-radius: 2px;
                background: ${i < strength ? strengthColors[strength - 1] : '#e0e0e0'};
                transition: background 0.3s ease;
            `;
            indicator.appendChild(bar);
        }

        // Add strength text
        const strengthText = document.createElement('span');
        strengthText.textContent = strengthLevels[strength - 1] || '';
        strengthText.style.cssText = `
            font-size: 0.8rem;
            color: ${strengthColors[strength - 1] || '#666'};
            margin-right: 0.5rem;
        `;
        indicator.appendChild(strengthText);

        passwordInput.parentElement.parentElement.appendChild(indicator);
    }

    function saveCredentials(email) {
        localStorage.setItem('rememberedEmail', email);
    }

    function clearSavedCredentials() {
        localStorage.removeItem('rememberedEmail');
    }

    function loadRememberedCredentials() {
        const rememberedEmail = localStorage.getItem('rememberedEmail');
        if (rememberedEmail) {
            emailInput.value = rememberedEmail;
            rememberCheckbox.checked = true;
            emailInput.classList.add('has-value');
        }
    }

    function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.message-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `message-notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(notification);

        // Auto remove after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideInRight 0.3s ease reverse';
                setTimeout(() => notification.remove(), 300);
            }
        }, 4000);
    }

    function getNotificationIcon(type) {
        switch(type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-exclamation-circle';
            case 'warning': return 'fa-exclamation-triangle';
            default: return 'fa-info-circle';
        }
    }

    // Add shake animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .input-wrapper input.has-value {
            background: white;
        }
    `;
    document.head.appendChild(style);
});
