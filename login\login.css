/* Login Page Specific Styles */

/* Back Button */
.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    font-family: 'Cairo', sans-serif;
}

.back-btn:hover {
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
    color: white;
}

.back-btn i {
    font-size: 1rem;
}

.back-btn span {
    font-size: 0.9rem;
}

/* Login Page Layout */
.login-page {
    min-height: 100vh;
    padding-top: 90px;
    background: linear-gradient(135deg, var(--light-gold) 0%, #f8f9fa 50%, var(--secondary-gold) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 90px 20px 20px;
}

.login-container {
    max-width: 1200px;
    width: 100%;
    background: white;
    border-radius: 25px;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 600px;
}

/* Login Form Section */
.login-form-section {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-section {
    margin-bottom: 2rem;
}

.logo-section i {
    font-size: 3rem;
    color: var(--primary-gold);
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3));
}

.logo-section h1 {
    font-size: 2rem;
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.logo-section p {
    color: var(--light-text);
    font-size: 1.1rem;
}

/* Form Styles */
.form-container {
    width: 100%;
}

.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--accent-blue);
    font-weight: 600;
    font-size: 0.95rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    right: 15px;
    color: var(--primary-gold);
    font-size: 1rem;
    z-index: 2;
}

.input-wrapper input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    background: rgba(255, 248, 220, 0.3);
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    background: white;
    transform: scale(1.02);
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--light-text);
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.3s ease;
    z-index: 2;
}

.toggle-password:hover {
    color: var(--primary-gold);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--dark-text);
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--primary-gold);
    border-radius: 4px;
    margin-left: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-gold);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-gold);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--dark-gold);
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.login-btn:hover {
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(212, 175, 55, 0.3);
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: var(--light-text);
    font-size: 0.9rem;
}

.social-login {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

.google-btn {
    color: #db4437;
}

.google-btn:hover {
    background: #db4437;
    color: white;
    border-color: #db4437;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(219, 68, 55, 0.3);
}

.facebook-btn {
    color: #4267B2;
}

.facebook-btn:hover {
    background: #4267B2;
    color: white;
    border-color: #4267B2;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 103, 178, 0.3);
}

.signup-link {
    text-align: center;
    color: var(--light-text);
    font-size: 0.9rem;
}

.signup-link a {
    color: var(--primary-gold);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.signup-link a:hover {
    color: var(--dark-gold);
    text-decoration: underline;
}

/* Enhanced Welcome Section */
.welcome-section {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 50%, var(--dark-gold) 100%);
    color: white;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Animated background elements */
.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255,255,255,0.05) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(212,175,55,0.1) 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
}

.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    animation: drift 30s linear infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.7;
    }
    33% {
        transform: translateY(-15px) rotate(120deg) scale(1.1);
        opacity: 0.9;
    }
    66% {
        transform: translateY(-10px) rotate(240deg) scale(0.9);
        opacity: 0.8;
    }
}

@keyframes drift {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(10px) translateY(-10px); }
    75% { transform: translateX(-5px) translateY(-15px); }
    100% { transform: translateX(0) translateY(0); }
}

.welcome-content {
    text-align: center;
    position: relative;
    z-index: 3;
    max-width: 400px;
}

.welcome-icon {
    margin-bottom: 2rem;
    position: relative;
}

.welcome-icon i {
    font-size: 5rem;
    color: var(--secondary-gold);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
    animation: pulse 3s ease-in-out infinite;
    position: relative;
}

.welcome-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: rotate 15s linear infinite;
}

.welcome-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: rotate 10s linear infinite reverse;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 12px 24px rgba(0, 0, 0, 0.4));
    }
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.welcome-content h2 {
    font-size: 2.8rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ffffff, var(--secondary-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textShine 4s ease-in-out infinite;
}

@keyframes textShine {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.welcome-content p {
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 0.95;
        transform: translateY(0);
    }
}

.features-list {
    display: grid;
    gap: 1.25rem;
    margin-top: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1.25rem;
    background: rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    animation: slideInLeft 0.6s ease-out;
}

.feature-item:nth-child(1) { animation-delay: 0.8s; }
.feature-item:nth-child(2) { animation-delay: 1s; }
.feature-item:nth-child(3) { animation-delay: 1.2s; }
.feature-item:nth-child(4) { animation-delay: 1.4s; }

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.feature-item:hover::before {
    left: 100%;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateX(-8px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.feature-item i {
    font-size: 1.75rem;
    color: var(--secondary-gold);
    min-width: 28px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
}

.feature-item:hover i {
    transform: scale(1.1) rotate(5deg);
    color: #ffffff;
}

.feature-item span {
    font-weight: 600;
    font-size: 1.05rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover span {
    transform: translateX(2px);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Message Notifications */
.message-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    color: var(--dark-text);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
    border-right: 4px solid var(--primary-gold);
}

.message-notification.success {
    border-right-color: #4CAF50;
}

.message-notification.error {
    border-right-color: #f44336;
}

.message-notification.warning {
    border-right-color: #ff9800;
}

.message-notification.info {
    border-right-color: #2196F3;
}

.message-notification i {
    font-size: 1.2rem;
}

.message-notification.success i {
    color: #4CAF50;
}

.message-notification.error i {
    color: #f44336;
}

.message-notification.warning i {
    color: #ff9800;
}

.message-notification.info i {
    color: #2196F3;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading State */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 968px) {
    .login-container {
        grid-template-columns: 1fr;
        max-width: 500px;
    }

    .welcome-section {
        order: -1;
        padding: 2rem;
    }

    .welcome-content h2 {
        font-size: 2rem;
    }

    .features-list {
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
    }

    .feature-item {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .feature-item i {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .login-page {
        padding: 90px 10px 10px;
    }

    .login-form-section {
        padding: 2rem 1.5rem;
    }

    .welcome-section {
        padding: 1.5rem;
    }

    .logo-section i {
        font-size: 2.5rem;
    }

    .logo-section h1 {
        font-size: 1.8rem;
    }

    .welcome-content h2 {
        font-size: 1.8rem;
    }

    .features-list {
        grid-template-columns: 1fr;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .social-login {
        gap: 0.75rem;
    }

    .social-btn {
        padding: 0.875rem;
        font-size: 0.9rem;
    }

    .back-btn span {
        display: none;
    }

    .back-btn {
        padding: 0.75rem;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        justify-content: center;
    }

    .message-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .login-container {
        border-radius: 15px;
        margin: 0 5px;
    }

    .login-form-section {
        padding: 1.5rem 1rem;
    }

    .welcome-section {
        padding: 1rem;
    }

    .input-wrapper input {
        padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    }

    .login-btn {
        padding: 0.875rem 1.5rem;
    }
}
