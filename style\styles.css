/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gold: #D4AF37;
    --secondary-gold: #F4E4BC;
    --dark-gold: #B8860B;
    --light-gold: #FFF8DC;
    --white: #FFFFFF;
    --dark-text: #2C2C2C;
    --light-text: #666666;
    --accent-blue: #1E3A8A;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 25px rgba(212, 175, 55, 0.15);
    border-bottom: 1px solid var(--secondary-gold);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-blue);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.logo i {
    margin-left: 10px;
    font-size: 2rem;
    color: var(--primary-gold);
    filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-text);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: var(--primary-gold);
    text-shadow: 0 1px 2px rgba(212, 175, 55, 0.3);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    transition: width 0.3s ease;
    border-radius: 1px;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Enhanced Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 50%, var(--dark-gold) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(212,175,55,0.1) 0%, transparent 70%);
    animation: heroFloat 25s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroPattern" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="rgba(255,255,255,0.08)"/><circle cx="6.25" cy="6.25" r="0.5" fill="rgba(255,255,255,0.04)"/><circle cx="18.75" cy="18.75" r="0.5" fill="rgba(255,255,255,0.04)"/></pattern></defs><rect width="100" height="100" fill="url(%23heroPattern)"/></svg>');
    animation: heroDrift 40s linear infinite;
}

@keyframes heroFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-10px) scale(1.05);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-5px) scale(0.95);
        opacity: 1;
    }
    75% {
        transform: translateY(-15px) scale(1.02);
        opacity: 0.85;
    }
}

@keyframes heroDrift {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-5px) translateY(-3px); }
    50% { transform: translateX(5px) translateY(-6px); }
    75% { transform: translateX(-3px) translateY(-9px); }
    100% { transform: translateX(0) translateY(0); }
}

.hero-content {
    flex: 1;
    padding: 2rem;
    position: relative;
    z-index: 3;
}

.hero-title {
    font-size: 4.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    background: linear-gradient(45deg, #ffffff, var(--secondary-gold), #ffffff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleShine 6s ease-in-out infinite, heroTitleFloat 4s ease-in-out infinite, fadeInUp 1s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    position: relative;
}

.hero-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 200%;
    animation: titleGlow 8s ease-in-out infinite;
    z-index: -1;
    border-radius: 10px;
}

@keyframes titleShine {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes titleGlow {
    0%, 100% {
        background-position: 0% 50%;
        opacity: 0.3;
    }
    50% {
        background-position: 100% 50%;
        opacity: 0.6;
    }
}

@keyframes heroTitleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.7;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 1s ease 0.2s both, subtitleFloat 6s ease-in-out infinite 2s;
}

@keyframes subtitleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-3px);
    }
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    animation: fadeInUp 1s ease 0.4s both, buttonsFloat 8s ease-in-out infinite 3s;
}

@keyframes buttonsFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    color: white;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid var(--secondary-gold);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    background: var(--secondary-gold);
    color: var(--accent-blue);
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(244, 228, 188, 0.3);
}

.hero-image {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.hero-bg {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-visual {
    width: 400px;
    height: 400px;
    position: relative;
    animation: heroVisualFloat 6s ease-in-out infinite;
}

.hero-visual::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
}

.hero-visual::after {
    content: '🦷';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12rem;
    opacity: 0.8;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
    animation: toothRotate 20s linear infinite;
}

@keyframes heroVisualFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-20px) scale(1.05);
    }
}

@keyframes toothRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.8;
    }
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 2px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, var(--light-gold) 100%);
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-gold), transparent);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(212, 175, 55, 0.1);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 15px 15px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
    border-color: var(--secondary-gold);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
    position: relative;
}

.service-icon::before {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--dark-gold), var(--primary-gold));
    z-index: -1;
}

.service-icon i {
    font-size: 2rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.service-card h3 {
    font-size: 1.5rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.service-card p {
    color: var(--light-text);
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 6rem 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    color: var(--accent-blue);
    margin-bottom: 1.5rem;
    position: relative;
}

.about-text h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 2px;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--light-text);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature i {
    color: var(--primary-gold);
    font-size: 1.2rem;
    filter: drop-shadow(0 1px 2px rgba(212, 175, 55, 0.3));
}

.about-cta {
    margin-top: 2rem;
}

.about-cta .btn {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.about-cta .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.about-image {
    position: relative;
}

.about-bg {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    position: relative;
}

.about-bg::after {
    content: '🦷';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8rem;
    opacity: 0.3;
}

/* Team Section */
.team {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--light-gold) 0%, #f8f9fa 100%);
    position: relative;
}

.team::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-gold), transparent);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(212, 175, 55, 0.1);
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 15px 15px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
    border-color: var(--secondary-gold);
}

.team-card:hover::before {
    opacity: 1;
}

.team-image {
    margin-bottom: 1.5rem;
}

.doctor-placeholder {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
    position: relative;
}

.doctor-placeholder::before {
    content: '';
    position: absolute;
    inset: 3px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-blue));
    z-index: -1;
}

.doctor-placeholder i {
    font-size: 3rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.team-card h3 {
    font-size: 1.5rem;
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.specialty {
    color: var(--primary-gold);
    font-weight: 600;
    margin-bottom: 1rem;
    text-shadow: 0 1px 2px rgba(212, 175, 55, 0.2);
}

/* Contact Section */
.contact {
    padding: 6rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item i {
    color: var(--primary-gold);
    font-size: 1.5rem;
    margin-top: 0.5rem;
    filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.contact-item h4 {
    color: var(--accent-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-gold) 100%);
    color: white;
    padding: 3rem 0 1rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-gold), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--secondary-gold);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--secondary-gold);
    text-shadow: 0 1px 2px rgba(244, 228, 188, 0.3);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--secondary-gold), var(--primary-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(244, 228, 188, 0.3);
}

.social-links a i {
    color: var(--accent-blue);
    font-weight: 600;
}

.social-links a:hover {
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #444;
    color: #ccc;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        right: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        right: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
}
