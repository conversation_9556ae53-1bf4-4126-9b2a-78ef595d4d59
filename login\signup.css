/* Signup Page Specific Styles */

.signup-container {
    grid-template-columns: 1fr 1fr;
    max-width: 1400px;
}

.signup-form {
    max-height: 70vh;
    overflow-y: auto;
    padding-left: 1rem;
}

/* Custom scrollbar for form */
.signup-form::-webkit-scrollbar {
    width: 6px;
}

.signup-form::-webkit-scrollbar-track {
    background: rgba(212, 175, 55, 0.1);
    border-radius: 3px;
}

.signup-form::-webkit-scrollbar-thumb {
    background: var(--primary-gold);
    border-radius: 3px;
}

.signup-form::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gold);
}

/* Form Row for side-by-side inputs */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Select styling */
.input-wrapper select {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    background: rgba(255, 248, 220, 0.3);
    cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23D4AF37" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: left 15px center;
    background-size: 16px;
}

.input-wrapper select:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
    background: white;
    transform: scale(1.02);
}

/* Date input styling */
.input-wrapper input[type="date"] {
    color-scheme: light;
}

.input-wrapper input[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23D4AF37" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
    background-size: 16px;
    cursor: pointer;
}

/* Terms agreement styling */
.terms-agreement {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--dark-text);
    line-height: 1.5;
    gap: 0.75rem;
}

.terms-agreement .checkmark {
    margin-left: 0;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.terms-link,
.privacy-link {
    color: var(--primary-gold);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.terms-link:hover,
.privacy-link:hover {
    color: var(--dark-gold);
    text-decoration: underline;
}

/* Signup button styling */
.signup-btn {
    background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
    margin-bottom: 1.5rem;
}

.signup-btn:hover {
    background: linear-gradient(135deg, var(--primary-gold), var(--accent-blue));
}

/* Phone number validation */
.input-wrapper input[type="tel"] {
    direction: ltr;
    text-align: right;
}

/* Form validation states */
.input-wrapper.valid input {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.05);
}

.input-wrapper.valid i {
    color: #4CAF50;
}

.input-wrapper.invalid input {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.05);
}

.input-wrapper.invalid i {
    color: #f44336;
}

/* Password strength indicator */
.password-strength {
    margin-top: 0.5rem;
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.strength-bar {
    width: 20px;
    height: 4px;
    border-radius: 2px;
    background: #e0e0e0;
    transition: background 0.3s ease;
}

.strength-bar.active {
    background: var(--primary-gold);
}

.strength-text {
    font-size: 0.8rem;
    color: var(--light-text);
    margin-right: 0.5rem;
}

/* Field validation messages */
.field-validation {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.field-validation.success {
    color: #4CAF50;
}

.field-validation.error {
    color: #f44336;
}

.field-validation.warning {
    color: #ff9800;
}

/* Progress indicator */
.form-progress {
    position: fixed;
    top: 90px;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(212, 175, 55, 0.2);
    z-index: 1000;
}

.form-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    width: 0%;
    transition: width 0.3s ease;
}

/* Responsive adjustments for signup */
@media (max-width: 968px) {
    .signup-container {
        grid-template-columns: 1fr;
        max-width: 600px;
    }
    
    .signup-form {
        max-height: none;
        overflow-y: visible;
        padding-left: 0;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .terms-agreement {
        font-size: 0.85rem;
    }
    
    .input-wrapper input,
    .input-wrapper select {
        padding: 0.875rem 2.5rem 0.875rem 0.875rem;
    }
}

@media (max-width: 480px) {
    .signup-container {
        margin: 0 5px;
    }
    
    .login-form-section {
        padding: 1.5rem 1rem;
    }
    
    .form-row {
        gap: 1rem;
    }
    
    .terms-agreement {
        font-size: 0.8rem;
        gap: 0.5rem;
    }
    
    .terms-agreement .checkmark {
        width: 16px;
        height: 16px;
    }
}

/* Animation for form steps */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-group {
    animation: slideInUp 0.3s ease;
}

/* Focus states for better accessibility */
.input-wrapper input:focus,
.input-wrapper select:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* Loading state for signup button */
.signup-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.signup-btn.loading .loading-spinner {
    margin-left: 0.5rem;
}

/* Success state animation */
.form-success {
    text-align: center;
    padding: 2rem;
    animation: slideInUp 0.5s ease;
}

.success-icon {
    font-size: 4rem;
    color: #4CAF50;
    margin-bottom: 1rem;
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.success-message {
    font-size: 1.2rem;
    color: var(--accent-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.success-description {
    color: var(--light-text);
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Email verification notice */
.verification-notice {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    color: #1976d2;
}

.verification-notice i {
    font-size: 1.2rem;
    color: #2196F3;
}
